<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>摄像头监控解决方案</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: white;
            font-family: Arial, sans-serif;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .config-panel {
            background: #2d2d2d;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: #ccc;
        }
        input {
            width: 100%;
            padding: 8px;
            border: 1px solid #555;
            border-radius: 4px;
            background: #444;
            color: white;
            box-sizing: border-box;
        }
        .solution-card {
            background: #333;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .solution-card h3 {
            margin-top: 0;
            color: #4CAF50;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 摄像头监控解决方案</h1>
        
        <div class="config-panel">
            <h3>摄像头配置</h3>
            <div class="form-group">
                <label>摄像头IP地址:</label>
                <input type="text" id="cameraIP" value="************">
            </div>
            <div class="form-group">
                <label>用户名:</label>
                <input type="text" id="username" value="admin">
            </div>
            <div class="form-group">
                <label>密码:</label>
                <input type="password" id="password" value="Yf.31306">
            </div>
        </div>

        <div class="warning">
            <strong>⚠️ 重要说明：</strong><br>
            1. 测试连接失败是正常的，这是浏览器安全策略导致的<br>
            2. 海康摄像头的这个URL只提供图片快照，不是视频流<br>
            3. 要实现"视频效果"需要不断刷新图片
        </div>

        <div class="solution-card">
            <h3>✅ 解决方案1：自动刷新监控页面</h3>
            <p>创建一个自动刷新的监控页面，模拟视频效果</p>
            <button onclick="createAutoRefreshPage()">创建自动刷新监控页面</button>
        </div>

        <div class="solution-card">
            <h3>✅ 解决方案2：直接访问（手动刷新）</h3>
            <p>直接在新标签页打开摄像头URL，手动刷新查看最新画面</p>
            <button onclick="openDirectUrl()">打开摄像头URL</button>
        </div>

        <div class="solution-card">
            <h3>✅ 解决方案3：多通道监控</h3>
            <p>同时监控多个摄像头通道</p>
            <button onclick="openChannel(1)">通道1</button>
            <button onclick="openChannel(101)">通道101</button>
            <button onclick="openChannel(102)">通道102</button>
        </div>

        <div class="info">
            <strong>💡 推荐使用方案1</strong> - 自动刷新监控页面，可以实现类似视频的效果
        </div>

        <div class="success" id="status" style="display:none;">
            操作完成！
        </div>
    </div>

    <script>
        function getBaseUrl() {
            var ip = document.getElementById('cameraIP').value;
            var username = document.getElementById('username').value;
            var password = document.getElementById('password').value;
            
            if (!ip || !username || !password) {
                alert('请填写完整信息');
                return null;
            }
            
            return 'http://' + username + ':' + password + '@' + ip;
        }

        function showStatus(message) {
            var status = document.getElementById('status');
            status.textContent = message;
            status.style.display = 'block';
            setTimeout(function() {
                status.style.display = 'none';
            }, 3000);
        }

        function openDirectUrl() {
            var baseUrl = getBaseUrl();
            if (!baseUrl) return;

            var url = baseUrl + '/ISAPI/Streaming/channels/101/picture';
            window.open(url, '_blank');
            showStatus('已打开摄像头URL，可以手动刷新页面查看最新画面');
        }

        function openChannel(channel) {
            var baseUrl = getBaseUrl();
            if (!baseUrl) return;

            var url = baseUrl + '/ISAPI/Streaming/channels/' + channel + '/picture';
            window.open(url, '_blank');
            showStatus('已打开通道' + channel);
        }

        function createAutoRefreshPage() {
            var baseUrl = getBaseUrl();
            if (!baseUrl) return;

            var url = baseUrl + '/ISAPI/Streaming/channels/101/picture';
            
            var newWindow = window.open('', '_blank', 'width=900,height=700');
            
            var html = '<!DOCTYPE html><html><head><meta charset="UTF-8">';
            html += '<title>摄像头自动刷新监控</title>';
            html += '<style>';
            html += 'body{margin:0;padding:20px;background:#000;color:#fff;text-align:center;font-family:Arial;}';
            html += 'img{max-width:90%;max-height:70vh;border:2px solid #555;border-radius:8px;}';
            html += '.controls{margin:20px 0;}';
            html += 'button{background:#007bff;color:#fff;border:none;padding:10px 20px;margin:5px;border-radius:4px;cursor:pointer;}';
            html += 'button:hover{background:#0056b3;}';
            html += '.status{background:#333;padding:10px;border-radius:4px;margin:10px 0;}';
            html += '.interval-control{margin:10px 0;}';
            html += 'input{padding:5px;margin:0 10px;border-radius:4px;border:1px solid #555;background:#444;color:#fff;}';
            html += '</style></head><body>';
            html += '<h1>📹 摄像头自动刷新监控</h1>';
            html += '<div class="status">自动刷新间隔: <span id="intervalDisplay">1000</span>ms</div>';
            html += '<img id="cameraImg" src="' + url + '" alt="摄像头画面">';
            html += '<div class="controls">';
            html += '<button onclick="toggleRefresh()" id="toggleBtn">暂停刷新</button>';
            html += '<button onclick="manualRefresh()">手动刷新</button>';
            html += '<button onclick="window.close()">关闭窗口</button>';
            html += '</div>';
            html += '<div class="interval-control">';
            html += '刷新间隔: <input type="number" id="intervalInput" value="1000" min="500" max="10000">';
            html += '<button onclick="updateInterval()">更新间隔</button>';
            html += '</div>';
            html += '<script>';
            html += 'var baseUrl = "' + url + '";';
            html += 'var refreshInterval = 1000;';
            html += 'var intervalId = null;';
            html += 'var isRunning = true;';
            html += 'function startRefresh() {';
            html += 'if (intervalId) clearInterval(intervalId);';
            html += 'intervalId = setInterval(function() {';
            html += 'if (isRunning) {';
            html += 'var img = document.getElementById("cameraImg");';
            html += 'img.src = baseUrl + "?t=" + Date.now();';
            html += '}';
            html += '}, refreshInterval);';
            html += '}';
            html += 'function toggleRefresh() {';
            html += 'isRunning = !isRunning;';
            html += 'var btn = document.getElementById("toggleBtn");';
            html += 'btn.textContent = isRunning ? "暂停刷新" : "开始刷新";';
            html += '}';
            html += 'function manualRefresh() {';
            html += 'var img = document.getElementById("cameraImg");';
            html += 'img.src = baseUrl + "?t=" + Date.now();';
            html += '}';
            html += 'function updateInterval() {';
            html += 'var newInterval = parseInt(document.getElementById("intervalInput").value);';
            html += 'if (newInterval >= 500 && newInterval <= 10000) {';
            html += 'refreshInterval = newInterval;';
            html += 'document.getElementById("intervalDisplay").textContent = refreshInterval;';
            html += 'startRefresh();';
            html += '} else {';
            html += 'alert("间隔必须在500-10000毫秒之间");';
            html += '}';
            html += '}';
            html += 'startRefresh();';
            html += '</script></body></html>';
            
            newWindow.document.write(html);
            newWindow.document.close();
            
            showStatus('已创建自动刷新监控页面！可以调整刷新间隔来控制"视频"流畅度');
        }
    </script>
</body>
</html>
