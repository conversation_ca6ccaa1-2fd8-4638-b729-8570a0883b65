<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>摄像头视频流播放解决方案</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: white;
            font-family: Arial, sans-serif;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .config-panel {
            background: #2d2d2d;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: #ccc;
        }
        input {
            width: 100%;
            padding: 8px;
            border: 1px solid #555;
            border-radius: 4px;
            background: #444;
            color: white;
            box-sizing: border-box;
        }
        .solution-card {
            background: #333;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .solution-card h3 {
            margin-top: 0;
            color: #4CAF50;
        }
        .solution-card.recommended {
            border-left-color: #28a745;
            background: #1e3a1e;
        }
        .solution-card.advanced {
            border-left-color: #ffc107;
            background: #3a3a1e;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #1e7e34;
        }
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        .btn-warning:hover {
            background: #e0a800;
        }
        .url-display {
            background: #444;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            word-break: break-all;
            margin: 10px 0;
            font-size: 12px;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        .video-container {
            background: #000;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
        video {
            max-width: 100%;
            height: auto;
            border: 2px solid #555;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 摄像头视频流播放解决方案</h1>
        
        <div class="config-panel">
            <h3>摄像头配置</h3>
            <div class="form-group">
                <label>摄像头IP地址:</label>
                <input type="text" id="cameraIP" value="************">
            </div>
            <div class="form-group">
                <label>用户名:</label>
                <input type="text" id="username" value="admin">
            </div>
            <div class="form-group">
                <label>密码:</label>
                <input type="password" id="password" value="Yf.31306">
            </div>
            <div class="form-group">
                <label>RTSP端口 (通常是554):</label>
                <input type="number" id="rtspPort" value="554">
            </div>
        </div>

        <div class="info">
            <strong>💡 关于视频流播放：</strong><br>
            海康摄像头支持RTSP视频流，但浏览器无法直接播放RTSP。需要转换为浏览器支持的格式（HLS、WebRTC、HTTP-FLV等）。
        </div>

        <div class="solution-card recommended">
            <h3>🥇 方案1：使用EasyPlayer播放器（推荐）</h3>
            <p>专业的网页视频播放器，支持RTSP、HLS、FLV等多种格式</p>
            <button class="btn-success" onclick="showEasyPlayerSolution()">查看EasyPlayer方案</button>
            <button onclick="openEasyPlayerDemo()">在线演示</button>
        </div>

        <div class="solution-card">
            <h3>🥈 方案2：RTSP转HLS流媒体服务</h3>
            <p>使用FFmpeg将RTSP转换为HLS格式，浏览器原生支持</p>
            <button onclick="showHLSSolution()">查看HLS方案</button>
            <button onclick="generateRTSPUrl()">生成RTSP地址</button>
        </div>

        <div class="solution-card advanced">
            <h3>🥉 方案3：WebRTC实时流媒体</h3>
            <p>最低延迟的解决方案，需要WebRTC服务器</p>
            <button class="btn-warning" onclick="showWebRTCSolution()">查看WebRTC方案</button>
        </div>

        <div class="solution-card">
            <h3>🔧 方案4：本地VLC播放器</h3>
            <p>使用VLC媒体播放器直接播放RTSP流</p>
            <button onclick="showVLCSolution()">查看VLC方案</button>
            <button onclick="openWithVLC()">用VLC打开</button>
        </div>

        <div id="solutionDetails" style="display:none;">
            <!-- 解决方案详情将在这里显示 -->
        </div>

        <div class="video-container" id="videoContainer" style="display:none;">
            <h3>视频播放区域</h3>
            <video id="videoPlayer" controls>
                您的浏览器不支持视频播放
            </video>
        </div>
    </div>

    <script>
        function getConfig() {
            return {
                ip: document.getElementById('cameraIP').value,
                username: document.getElementById('username').value,
                password: document.getElementById('password').value,
                port: document.getElementById('rtspPort').value
            };
        }

        function generateRTSPUrl() {
            var config = getConfig();
            if (!config.ip || !config.username || !config.password) {
                alert('请填写完整配置');
                return;
            }

            var rtspUrls = [
                'rtsp://' + config.username + ':' + config.password + '@' + config.ip + ':' + config.port + '/Streaming/Channels/101',
                'rtsp://' + config.username + ':' + config.password + '@' + config.ip + ':' + config.port + '/Streaming/Channels/1',
                'rtsp://' + config.username + ':' + config.password + '@' + config.ip + ':' + config.port + '/h264/ch1/main/av_stream'
            ];

            var html = '<div class="solution-card"><h3>生成的RTSP地址</h3>';
            html += '<p>请尝试以下RTSP地址（海康摄像头常用格式）：</p>';
            
            rtspUrls.forEach(function(url, index) {
                html += '<div class="url-display">格式' + (index + 1) + ': ' + url + '</div>';
            });
            
            html += '<p><strong>使用方法：</strong></p>';
            html += '<ul>';
            html += '<li>复制上述任一地址到VLC播放器中</li>';
            html += '<li>或使用专业的流媒体服务器进行转换</li>';
            html += '<li>或使用支持RTSP的网页播放器</li>';
            html += '</ul></div>';

            document.getElementById('solutionDetails').innerHTML = html;
            document.getElementById('solutionDetails').style.display = 'block';
        }

        function showEasyPlayerSolution() {
            var html = '<div class="solution-card"><h3>EasyPlayer解决方案</h3>';
            html += '<p><strong>EasyPlayer</strong>是专业的网页视频播放器，支持多种视频流格式：</p>';
            html += '<ul>';
            html += '<li>✅ 支持RTSP、RTMP、HLS、FLV等格式</li>';
            html += '<li>✅ 无需插件，纯JavaScript实现</li>';
            html += '<li>✅ 低延迟，高性能</li>';
            html += '<li>✅ 支持海康、大华等主流摄像头</li>';
            html += '</ul>';
            html += '<p><strong>实施步骤：</strong></p>';
            html += '<ol>';
            html += '<li>下载EasyPlayer.js播放器</li>';
            html += '<li>部署EasyDarwin流媒体服务器</li>';
            html += '<li>配置RTSP转换服务</li>';
            html += '<li>在网页中集成播放器</li>';
            html += '</ol>';
            html += '<div class="url-display">官网: https://www.easydarwin.org/</div>';
            html += '</div>';

            document.getElementById('solutionDetails').innerHTML = html;
            document.getElementById('solutionDetails').style.display = 'block';
        }

        function showHLSSolution() {
            var html = '<div class="solution-card"><h3>HLS流媒体解决方案</h3>';
            html += '<p><strong>HLS (HTTP Live Streaming)</strong>是苹果开发的流媒体协议，浏览器原生支持：</p>';
            html += '<p><strong>服务器端配置（需要技术人员）：</strong></p>';
            html += '<ol>';
            html += '<li>安装FFmpeg</li>';
            html += '<li>使用以下命令转换RTSP到HLS：</li>';
            html += '</ol>';
            html += '<div class="url-display">ffmpeg -i rtsp://admin:password@IP:554/Streaming/Channels/101 -c copy -f hls -hls_time 2 -hls_list_size 3 -hls_flags delete_segments output.m3u8</div>';
            html += '<p><strong>网页端播放：</strong></p>';
            html += '<div class="url-display">&lt;video controls&gt;<br>&nbsp;&nbsp;&lt;source src="http://your-server/output.m3u8" type="application/x-mpegURL"&gt;<br>&lt;/video&gt;</div>';
            html += '<p><strong>优点：</strong>浏览器原生支持，无需插件</p>';
            html += '<p><strong>缺点：</strong>需要服务器端配置，有一定延迟</p>';
            html += '</div>';

            document.getElementById('solutionDetails').innerHTML = html;
            document.getElementById('solutionDetails').style.display = 'block';
        }

        function showWebRTCSolution() {
            var html = '<div class="solution-card"><h3>WebRTC实时流媒体解决方案</h3>';
            html += '<p><strong>WebRTC</strong>提供最低延迟的实时视频传输：</p>';
            html += '<p><strong>推荐的WebRTC服务：</strong></p>';
            html += '<ul>';
            html += '<li>🔥 <strong>Janus WebRTC Gateway</strong> - 开源解决方案</li>';
            html += '<li>🔥 <strong>Kurento Media Server</strong> - 企业级解决方案</li>';
            html += '<li>🔥 <strong>SRS (Simple Realtime Server)</strong> - 轻量级解决方案</li>';
            html += '</ul>';
            html += '<p><strong>实施复杂度：</strong>⭐⭐⭐⭐⭐ (需要专业技术人员)</p>';
            html += '<p><strong>优点：</strong></p>';
            html += '<ul>';
            html += '<li>✅ 超低延迟（通常&lt;500ms）</li>';
            html += '<li>✅ 浏览器原生支持</li>';
            html += '<li>✅ 双向通信支持</li>';
            html += '</ul>';
            html += '<p><strong>缺点：</strong></p>';
            html += '<ul>';
            html += '<li>❌ 配置复杂</li>';
            html += '<li>❌ 需要专业服务器</li>';
            html += '<li>❌ 开发成本高</li>';
            html += '</ul>';
            html += '</div>';

            document.getElementById('solutionDetails').innerHTML = html;
            document.getElementById('solutionDetails').style.display = 'block';
        }

        function showVLCSolution() {
            var config = getConfig();
            var rtspUrl = 'rtsp://' + config.username + ':' + config.password + '@' + config.ip + ':' + config.port + '/Streaming/Channels/101';
            
            var html = '<div class="solution-card"><h3>VLC播放器解决方案</h3>';
            html += '<p><strong>VLC媒体播放器</strong>是最简单的RTSP播放解决方案：</p>';
            html += '<p><strong>使用步骤：</strong></p>';
            html += '<ol>';
            html += '<li>下载并安装VLC播放器</li>';
            html += '<li>打开VLC，选择"媒体" → "打开网络串流"</li>';
            html += '<li>输入RTSP地址：</li>';
            html += '</ol>';
            html += '<div class="url-display">' + rtspUrl + '</div>';
            html += '<p><strong>优点：</strong></p>';
            html += '<ul>';
            html += '<li>✅ 免费且功能强大</li>';
            html += '<li>✅ 支持几乎所有视频格式</li>';
            html += '<li>✅ 无需额外配置</li>';
            html += '<li>✅ 低延迟</li>';
            html += '</ul>';
            html += '<p><strong>缺点：</strong></p>';
            html += '<ul>';
            html += '<li>❌ 需要安装客户端软件</li>';
            html += '<li>❌ 不是网页解决方案</li>';
            html += '</ul>';
            html += '<div class="url-display">VLC下载: https://www.videolan.org/vlc/</div>';
            html += '</div>';

            document.getElementById('solutionDetails').innerHTML = html;
            document.getElementById('solutionDetails').style.display = 'block';
        }

        function openEasyPlayerDemo() {
            window.open('https://www.easydarwin.org/demo/', '_blank');
        }

        function openWithVLC() {
            var config = getConfig();
            if (!config.ip || !config.username || !config.password) {
                alert('请填写完整配置');
                return;
            }
            
            var rtspUrl = 'rtsp://' + config.username + ':' + config.password + '@' + config.ip + ':' + config.port + '/Streaming/Channels/101';
            
            // 尝试使用vlc://协议打开（如果系统已安装VLC）
            var vlcUrl = 'vlc://' + rtspUrl;
            window.location.href = vlcUrl;
            
            // 同时显示手动操作说明
            setTimeout(function() {
                alert('如果VLC没有自动打开，请：\n1. 手动打开VLC播放器\n2. 选择"媒体" → "打开网络串流"\n3. 输入地址：' + rtspUrl);
            }, 1000);
        }
    </script>
</body>
</html>
