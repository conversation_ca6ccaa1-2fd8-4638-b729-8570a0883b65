<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>摄像头监控 - 最终版</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: white;
            font-family: Arial, sans-serif;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        .config-panel {
            background: #2d2d2d;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: left;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: #ccc;
        }
        input {
            width: 100%;
            padding: 8px;
            border: 1px solid #555;
            border-radius: 4px;
            background: #444;
            color: white;
            box-sizing: border-box;
        }
        .solution-card {
            background: #333;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .solution-card h3 {
            margin-top: 0;
            color: #4CAF50;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 15px 25px;
            margin: 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        .url-display {
            background: #444;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            word-break: break-all;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 摄像头监控系统</h1>
        
        <div class="config-panel">
            <h3>摄像头配置</h3>
            <div class="form-group">
                <label>摄像头IP地址:</label>
                <input type="text" id="cameraIP" value="************">
            </div>
            <div class="form-group">
                <label>用户名:</label>
                <input type="text" id="username" value="admin">
            </div>
            <div class="form-group">
                <label>密码:</label>
                <input type="password" id="password" value="Yf.31306">
            </div>
        </div>

        <div class="warning">
            <strong>⚠️ 重要说明：</strong><br>
            • 海康摄像头URL只提供图片快照，不是视频流<br>
            • 需要不断刷新图片来模拟视频效果<br>
            • 浏览器安全策略会阻止JavaScript测试连接（这是正常的）
        </div>

        <div class="solution-card">
            <h3>✅ 方案1：直接访问摄像头（推荐）</h3>
            <p>在新标签页直接打开摄像头URL，手动刷新查看最新画面</p>
            <button onclick="openCamera()">打开摄像头</button>
            <div id="cameraUrl" class="url-display" style="display:none;"></div>
        </div>

        <div class="solution-card">
            <h3>✅ 方案2：多通道监控</h3>
            <p>同时打开多个摄像头通道</p>
            <button onclick="openChannel(1)">通道 1</button>
            <button onclick="openChannel(101)">通道 101</button>
            <button onclick="openChannel(102)">通道 102</button>
        </div>

        <div class="solution-card">
            <h3>✅ 方案3：自动刷新监控</h3>
            <p>创建一个自动刷新的监控页面</p>
            <button onclick="createMonitorPage()">创建监控页面</button>
        </div>

        <div class="info">
            <strong>💡 使用建议：</strong><br>
            1. 先试试"方案1：直接访问摄像头"<br>
            2. 如果需要自动刷新效果，使用"方案3：自动刷新监控"<br>
            3. 可以同时打开多个通道进行对比监控
        </div>
    </div>
</body>

<script>
function getConfig() {
    var ip = document.getElementById('cameraIP').value;
    var username = document.getElementById('username').value;
    var password = document.getElementById('password').value;
    
    if (!ip || !username || !password) {
        alert('请填写完整的摄像头信息');
        return null;
    }
    
    return {
        ip: ip,
        username: username,
        password: password
    };
}

function buildUrl(config, channel) {
    return 'http://' + config.username + ':' + config.password + '@' + config.ip + '/ISAPI/Streaming/channels/' + channel + '/picture';
}

function openCamera() {
    var config = getConfig();
    if (!config) return;
    
    var url = buildUrl(config, 101);
    window.open(url, '_blank');
    
    var urlDiv = document.getElementById('cameraUrl');
    urlDiv.textContent = '已打开: ' + url;
    urlDiv.style.display = 'block';
}

function openChannel(channel) {
    var config = getConfig();
    if (!config) return;
    
    var url = buildUrl(config, channel);
    window.open(url, '_blank');
}

function createMonitorPage() {
    var config = getConfig();
    if (!config) return;
    
    var url = buildUrl(config, 101);
    var monitorUrl = 'data:text/html;charset=utf-8,' + encodeURIComponent(createMonitorHTML(url));
    window.open(monitorUrl, '_blank');
}

function createMonitorHTML(imageUrl) {
    return '<!DOCTYPE html>' +
    '<html><head><meta charset="UTF-8">' +
    '<title>摄像头自动监控</title>' +
    '<style>' +
    'body{margin:0;padding:20px;background:#000;color:#fff;text-align:center;font-family:Arial,sans-serif;}' +
    'img{max-width:90%;max-height:70vh;border:2px solid #555;border-radius:8px;}' +
    '.controls{margin:20px 0;}' +
    'button{background:#007bff;color:#fff;border:none;padding:10px 20px;margin:5px;border-radius:4px;cursor:pointer;}' +
    'button:hover{background:#0056b3;}' +
    '.status{background:#333;padding:10px;border-radius:4px;margin:10px 0;}' +
    '</style></head><body>' +
    '<h1>📹 摄像头自动监控</h1>' +
    '<div class="status">自动刷新中 - 间隔: 1秒</div>' +
    '<img id="camera" src="' + imageUrl + '" alt="摄像头画面">' +
    '<div class="controls">' +
    '<button onclick="toggleAuto()">暂停/继续</button>' +
    '<button onclick="refreshNow()">立即刷新</button>' +
    '<button onclick="window.close()">关闭</button>' +
    '</div>' +
    '<script>' +
    'var imageUrl="' + imageUrl + '";' +
    'var autoRefresh=true;' +
    'var timer=setInterval(function(){' +
    'if(autoRefresh){' +
    'document.getElementById("camera").src=imageUrl+"?t="+Date.now();' +
    '}' +
    '},1000);' +
    'function toggleAuto(){' +
    'autoRefresh=!autoRefresh;' +
    'document.querySelector(".status").textContent=autoRefresh?"自动刷新中 - 间隔: 1秒":"已暂停自动刷新";' +
    '}' +
    'function refreshNow(){' +
    'document.getElementById("camera").src=imageUrl+"?t="+Date.now();' +
    '}' +
    '</scr' + 'ipt></body></html>';
}
</script>
</html>
